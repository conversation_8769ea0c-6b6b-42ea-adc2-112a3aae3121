'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
interface BlogPost {
  id: string
  title: string
  slug: string
  description: string
  hero_image_url: string
  hero_image_alt: string
  category: string
  published_at: string
  tags: string[]
}

interface BlogSidebarProps {
  currentSlug: string
  currentCategory: string
}

const BlogSidebar: React.FC<BlogSidebarProps> = ({ currentSlug, currentCategory }) => {
  const [loading, setLoading] = useState(false)

  // Removed blog data fetching since we're removing blog sections





  return (
    <aside className="space-y-8">

      {/* Newsletter Signup */}
      <div className="bg-gradient-to-br from-accent to-accent rounded-xl p-6 text-white" style={{ background: `linear-gradient(to bottom right, var(--accent), var(--accent))` }}>
        <h3 className="text-xl font-bold mb-3">Stay Updated</h3>
        <p className="text-white/80 text-sm mb-4">
          Get the latest travel insights and safari adventures delivered to your inbox.
        </p>

        <form className="space-y-3">
          <input
            type="email"
            placeholder="Enter your email"
            className="w-full px-4 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/30"
          />
          <button
            type="submit"
            className="w-full bg-white text-accent font-semibold py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors"
            style={{ color: 'var(--accent)' }}
          >
            Subscribe
          </button>
        </form>
      </div>

      {/* Categories */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          Categories
        </h3>
        
        <div className="space-y-2">
          {['Wildlife', 'Culture', 'Adventure', 'Conservation', 'Travel Tips'].map((category) => (
            <Link
              key={category}
              href={`/blog?category=${category.toLowerCase()}`}
              className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-accent rounded-lg transition-colors"
            >
              {category}
            </Link>
          ))}
        </div>
      </div>
    </aside>
  )
}

export default BlogSidebar
